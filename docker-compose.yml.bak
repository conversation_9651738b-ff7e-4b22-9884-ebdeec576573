version: '3.9'

services:
  db:
    image: mariadb:10.6
    container_name: mariadb
    restart: no
    environment:
      MYSQL_ROOT_PASSWORD: wofaintle!23
      MYSQL_DATABASE: wordpressdb
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wofaintle!23
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - wpsite

  wordpress:
    depends_on:
      - db
    image: wordpress:latest
    container_name: wordpress
    restart: no
    ports:
      - "8000:80"
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wofaintle!23
      WORDPRESS_DB_NAME: wordpressdb
    volumes:
      - ./wordpress:/var/www/html  # 挂载整个WordPress目录
      - ./plugins:/var/www/html/wp-content/plugins
      - ./themes:/var/www/html/wp-content/themes
    networks:
      - wpsite

networks:
  wpsite:

volumes:
  db_data:
