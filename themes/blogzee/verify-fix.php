<?php
/**
 * Simple verification script for the vote counting bug fix
 * 
 * This script creates a clean test scenario to verify the fix works
 */

// Load WordPress
require_once('../../wordpress/wp-load.php');

// Only allow admin access
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Vote Counting Bug Fix Verification</h1>\n";

global $wpdb;
$table_name = $wpdb->prefix . 'blogzee_post_likes';

// Get first post
$post = get_posts(['numberposts' => 1, 'post_status' => 'publish']);
if (empty($post)) {
    die('No published posts found. Please create a post first.');
}

$post_id = $post[0]->ID;
echo "<p><strong>Testing with Post ID:</strong> {$post_id} (\"" . esc_html($post[0]->post_title) . "\")</p>\n";

// Clear existing votes for clean test
$wpdb->delete($table_name, ['post_id' => $post_id]);
echo "<p>✅ Cleared existing votes</p>\n";

// Test 1: Initial state should be 0,0
$initial = blogzee_get_post_vote_counts($post_id);
echo "<p><strong>Initial state:</strong> Likes={$initial['like_count']}, Dislikes={$initial['dislike_count']}</p>\n";

if ($initial['like_count'] != 0 || $initial['dislike_count'] != 0) {
    echo "<p>❌ ERROR: Initial state should be 0,0</p>\n";
    exit;
}

// Test 2: First user votes "like"
echo "<h3>Test 1: First user votes 'like'</h3>\n";

wp_set_current_user(1);
$_POST = [
    'action' => 'blogzee_post_vote',
    'post_id' => $post_id,
    'vote_type' => 'like',
    'nonce' => wp_create_nonce('blogzee-security-nonce')
];

ob_start();
blogzee_handle_post_vote();
$response1 = ob_get_clean();

$after_first = blogzee_get_post_vote_counts($post_id);
echo "<p><strong>After first vote:</strong> Likes={$after_first['like_count']}, Dislikes={$after_first['dislike_count']}</p>\n";

if ($after_first['like_count'] == 1 && $after_first['dislike_count'] == 0) {
    echo "<p>✅ PASS: First vote recorded correctly</p>\n";
} else {
    echo "<p>❌ FAIL: Expected Likes=1, Dislikes=0</p>\n";
    exit;
}

// Test 3: Second user votes "like" (this is where the bug occurred)
echo "<h3>Test 2: Second user votes 'like' (Bug test)</h3>\n";

wp_set_current_user(2);
$_POST = [
    'action' => 'blogzee_post_vote',
    'post_id' => $post_id,
    'vote_type' => 'like',
    'nonce' => wp_create_nonce('blogzee-security-nonce')
];

ob_start();
blogzee_handle_post_vote();
$response2 = ob_get_clean();

$after_second = blogzee_get_post_vote_counts($post_id);
echo "<p><strong>After second vote:</strong> Likes={$after_second['like_count']}, Dislikes={$after_second['dislike_count']}</p>\n";

// This is the critical test - before the fix, this would show like=2, dislike=1
if ($after_second['like_count'] == 2 && $after_second['dislike_count'] == 0) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>\n";
    echo "<h3>✅ SUCCESS: Bug is FIXED!</h3>\n";
    echo "<p>Expected: Likes=2, Dislikes=0</p>\n";
    echo "<p>Actual: Likes={$after_second['like_count']}, Dislikes={$after_second['dislike_count']}</p>\n";
    echo "<p>The vote counting is now working correctly.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>\n";
    echo "<h3>❌ FAILED: Bug still exists!</h3>\n";
    echo "<p>Expected: Likes=2, Dislikes=0</p>\n";
    echo "<p>Actual: Likes={$after_second['like_count']}, Dislikes={$after_second['dislike_count']}</p>\n";
    echo "<p>The original bug would show: Likes=2, Dislikes=1</p>\n";
    echo "</div>\n";
}

// Show database state for debugging
echo "<h3>Database Debug Info</h3>\n";
$all_votes = $wpdb->get_results($wpdb->prepare(
    "SELECT user_id, vote_type, vote_time FROM $table_name WHERE post_id = %d ORDER BY user_id",
    $post_id
));

if ($all_votes) {
    echo "<pre>\n";
    foreach ($all_votes as $vote) {
        echo "User {$vote->user_id}: {$vote->vote_type} at {$vote->vote_time}\n";
    }
    echo "</pre>\n";
} else {
    echo "<p>No votes found in database.</p>\n";
}

// Reset user
wp_set_current_user(1);

echo "<h3>Summary</h3>\n";
echo "<p>The bug was caused by using 'INSERT ... ON DUPLICATE KEY UPDATE' when inserting new votes.</p>\n";
echo "<p>The fix replaces this with a simple INSERT statement since we already check for existing votes.</p>\n";
echo "<p>This ensures that vote counts remain accurate and don't get corrupted.</p>\n";
?>
