<?php
/**
 * Test script to verify the like/dislike vote counting bug fix
 * 
 * This script simulates the exact scenario described by the user:
 * - Start with a post that has 0 likes and 0 dislikes
 * - User clicks "like" 
 * - Expected result: like=1, dislike=0
 * - Bug result: like=2, dislike=1
 * 
 * Usage: Access this file directly in browser while logged in as admin
 */

// Prevent direct access if not in WordPress context
if (!defined('ABSPATH')) {
    // Load WordPress - adjust path based on theme location
    $wp_load_paths = [
        '../../../wp-load.php',  // Standard WordPress structure
        '../../wp-load.php',     // Alternative structure
        '../wp-load.php',        // Another alternative
        './wp-load.php'          // Root level
    ];

    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once($path);
            $wp_loaded = true;
            break;
        }
    }

    if (!$wp_loaded) {
        die('WordPress not found. Please run this script from within WordPress or adjust the wp-load.php path.');
    }
}

// Only allow admin access
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Admin privileges required.');
}

// Get the functions we need
if (!function_exists('blogzee_get_post_vote_counts')) {
    wp_die('Blogzee voting functions not found. Make sure the theme is active.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Blogzee Vote Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
        .vote-btn { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .like-btn { background: #28a745; color: white; border: none; }
        .dislike-btn { background: #dc3545; color: white; border: none; }
    </style>
</head>
<body>
    <h1>Blogzee Vote Counting Bug Fix Test</h1>
    
    <?php
    if (isset($_GET['run_test'])) {
        echo '<div class="test-section info">';
        echo '<h2>🧪 Running Test Scenario</h2>';
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'blogzee_post_likes';
        
        // Get first published post
        $post = get_posts(['numberposts' => 1, 'post_status' => 'publish']);
        if (empty($post)) {
            echo '<p class="error">❌ No published posts found. Please create a post first.</p>';
            echo '</div>';
            return;
        }
        
        $post_id = $post[0]->ID;
        echo '<p><strong>Testing with Post ID:</strong> ' . $post_id . ' ("' . esc_html($post[0]->post_title) . '")</p>';
        
        // Clear any existing votes for this post
        $wpdb->delete($table_name, ['post_id' => $post_id]);
        echo '<p>✅ Cleared existing votes for test post</p>';
        
        // Step 1: Check initial state (should be 0,0)
        $initial_counts = blogzee_get_post_vote_counts($post_id);
        echo '<p><strong>Initial state:</strong> Likes=' . $initial_counts['like_count'] . ', Dislikes=' . $initial_counts['dislike_count'] . '</p>';
        
        // Step 2: Simulate first user (ID=1) clicking "Like"
        echo '<h3>Step 1: User 1 clicks "Like"</h3>';
        
        // Set current user to user 1
        wp_set_current_user(1);
        
        // Simulate AJAX request
        $_POST = [
            'action' => 'blogzee_post_vote',
            'post_id' => $post_id,
            'vote_type' => 'like',
            'nonce' => wp_create_nonce('blogzee-security-nonce')
        ];
        
        // Capture output
        ob_start();
        try {
            blogzee_handle_post_vote();
        } catch (Exception $e) {
            echo '<p class="error">❌ Exception: ' . esc_html($e->getMessage()) . '</p>';
        }
        $ajax_output = ob_get_clean();
        
        // Check counts after first vote
        $after_first_vote = blogzee_get_post_vote_counts($post_id);
        echo '<p><strong>After User 1 votes:</strong> Likes=' . $after_first_vote['like_count'] . ', Dislikes=' . $after_first_vote['dislike_count'] . '</p>';
        
        // Verify first vote is correct
        if ($after_first_vote['like_count'] == 1 && $after_first_vote['dislike_count'] == 0) {
            echo '<p class="success">✅ First vote recorded correctly</p>';
        } else {
            echo '<p class="error">❌ First vote incorrect! Expected: Likes=1, Dislikes=0</p>';
        }
        
        // Step 3: Simulate second user (ID=2) clicking "Like" on the same post
        echo '<h3>Step 2: User 2 clicks "Like" (This is where the bug occurred)</h3>';
        
        // Set current user to user 2
        wp_set_current_user(2);
        
        // Simulate AJAX request
        $_POST = [
            'action' => 'blogzee_post_vote',
            'post_id' => $post_id,
            'vote_type' => 'like',
            'nonce' => wp_create_nonce('blogzee-security-nonce')
        ];
        
        // Capture output
        ob_start();
        try {
            blogzee_handle_post_vote();
        } catch (Exception $e) {
            echo '<p class="error">❌ Exception: ' . esc_html($e->getMessage()) . '</p>';
        }
        $ajax_output2 = ob_get_clean();
        
        // Check final counts
        $final_counts = blogzee_get_post_vote_counts($post_id);
        echo '<p><strong>After User 2 votes:</strong> Likes=' . $final_counts['like_count'] . ', Dislikes=' . $final_counts['dislike_count'] . '</p>';
        
        // Verify the fix
        echo '<h3>🎯 Test Result</h3>';
        if ($final_counts['like_count'] == 2 && $final_counts['dislike_count'] == 0) {
            echo '<div class="success">';
            echo '<h4>✅ SUCCESS: Bug is FIXED!</h4>';
            echo '<p>Expected: Likes=2, Dislikes=0</p>';
            echo '<p>Actual: Likes=' . $final_counts['like_count'] . ', Dislikes=' . $final_counts['dislike_count'] . '</p>';
            echo '<p>The vote counting is now working correctly.</p>';
            echo '</div>';
        } else {
            echo '<div class="error">';
            echo '<h4>❌ FAILED: Bug still exists!</h4>';
            echo '<p>Expected: Likes=2, Dislikes=0</p>';
            echo '<p>Actual: Likes=' . $final_counts['like_count'] . ', Dislikes=' . $final_counts['dislike_count'] . '</p>';
            echo '<p>The bug is still present and needs further investigation.</p>';
            echo '</div>';
        }
        
        // Show all votes in database for debugging
        echo '<h3>🔍 Database Debug Info</h3>';
        $all_votes = $wpdb->get_results($wpdb->prepare(
            "SELECT user_id, vote_type, vote_time FROM $table_name WHERE post_id = %d ORDER BY user_id",
            $post_id
        ));
        
        if ($all_votes) {
            echo '<pre>';
            foreach ($all_votes as $vote) {
                echo "User {$vote->user_id}: {$vote->vote_type} at {$vote->vote_time}\n";
            }
            echo '</pre>';
        } else {
            echo '<p>No votes found in database.</p>';
        }
        
        // Reset user
        wp_set_current_user(1);
        
        echo '</div>';
    } else {
        // Show test interface
        echo '<div class="test-section">';
        echo '<h2>🚀 Ready to Test</h2>';
        echo '<p>This test will simulate the exact bug scenario:</p>';
        echo '<ol>';
        echo '<li>Clear all votes for a test post</li>';
        echo '<li>User 1 clicks "Like" (should result in: Likes=1, Dislikes=0)</li>';
        echo '<li>User 2 clicks "Like" (should result in: Likes=2, Dislikes=0)</li>';
        echo '</ol>';
        echo '<p><strong>Before the fix:</strong> Step 3 would incorrectly show Likes=2, Dislikes=1</p>';
        echo '<p><strong>After the fix:</strong> Step 3 should correctly show Likes=2, Dislikes=0</p>';
        echo '<p><a href="?run_test=1" class="vote-btn like-btn">🧪 Run Test</a></p>';
        echo '</div>';
        
        // Show current database state
        echo '<div class="test-section">';
        echo '<h2>📊 Current Database State</h2>';
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'blogzee_post_likes';
        
        $total_votes = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        echo '<p><strong>Total votes in database:</strong> ' . $total_votes . '</p>';
        
        if ($total_votes > 0) {
            $recent_votes = $wpdb->get_results("SELECT post_id, user_id, vote_type, vote_time FROM $table_name ORDER BY vote_time DESC LIMIT 10");
            echo '<h4>Recent votes:</h4>';
            echo '<pre>';
            foreach ($recent_votes as $vote) {
                echo "Post {$vote->post_id}, User {$vote->user_id}: {$vote->vote_type} at {$vote->vote_time}\n";
            }
            echo '</pre>';
        }
        echo '</div>';
    }
    ?>
    
    <div class="test-section">
        <h2>📝 About This Test</h2>
        <p>This test script helps verify that the like/dislike counting bug has been fixed.</p>
        <p><strong>The Bug:</strong> When a new user clicked "like" on a post with existing votes, the system would incorrectly show like=2, dislike=1 instead of the correct counts.</p>
        <p><strong>The Fix:</strong> Replaced the problematic "INSERT ... ON DUPLICATE KEY UPDATE" with a simple INSERT statement, since we already check for existing votes.</p>
        <p><strong>Root Cause:</strong> The ON DUPLICATE KEY UPDATE clause was causing unexpected behavior in the vote counting logic.</p>
    </div>
    
</body>
</html>
